import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import Blog from '@/pages/public/blog';
import { router } from '@inertiajs/react';
import { SELECT_ALL_CATEGORIES } from '@/lib/utils';

// Mock the router and other Inertia components
vi.mock('@inertiajs/react', () => ({
  router: {
    get: vi.fn(),
  },
  usePage: () => ({
    props: {
      auth: { user: null },
    },
    url: '/blog',
    component: 'Blog',
  }),
  Link: ({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
  Head: ({ children }: { children?: React.ReactNode }) => React.createElement('div', { 'data-testid': 'head' }, children),
  createInertiaApp: vi.fn(),
  createProvider: vi.fn(() => ({
    Provider: ({ children }: { children: React.ReactNode }) => React.createElement('div', {}, children),
  })),
}));

// Mock the PublicLayout
vi.mock('@/layouts/public-layout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="public-layout">{children}</div>,
}));

describe('Blog Page', () => {
  const mockBlogPosts = {
    data: [
      {
        id: 1,
        title: 'Meta Ads Best Practices',
        slug: 'meta-ads-best-practices',
        excerpt: 'Learn the best practices for Meta advertising campaigns',
        published_at: '2025-01-15T10:00:00Z',
        category: { id: 1, name: 'Meta Advertising' },
        author: { name: 'John Doe' },
        featured_image: 'meta-ads.jpg',
        views_count: 150,
      },
      {
        id: 2,
        title: 'Facebook Pixel Implementation Guide',
        slug: 'facebook-pixel-guide',
        excerpt: 'Complete guide to implementing Facebook Pixel',
        published_at: '2025-01-10T10:00:00Z',
        category: { id: 2, name: 'Tracking & Analytics' },
        author: { name: 'Jane Smith' },
        featured_image: 'pixel-guide.jpg',
        views_count: 200,
      },
    ],
    current_page: 1,
    last_page: 3,
    prev_page_url: null,
    next_page_url: 'http://example.com/blog?page=2',
    links: [
      { url: null, label: '&laquo; Previous', active: false },
      { url: 'http://example.com/blog?page=1', label: '1', active: true },
      { url: 'http://example.com/blog?page=2', label: '2', active: false },
      { url: 'http://example.com/blog?page=3', label: '3', active: false },
      { url: 'http://example.com/blog?page=2', label: 'Next &raquo;', active: false },
    ],
  };

  const mockCategories = [
    { id: 1, name: 'Meta Advertising', slug: 'meta-advertising' },
    { id: 2, name: 'Tracking & Analytics', slug: 'tracking-analytics' },
    { id: 3, name: 'Consulting', slug: 'consulting' },
  ];

  const mockFilters = {
    search: '',
    category_id: SELECT_ALL_CATEGORIES,
    sort_by: 'published_at',
    sort_direction: 'desc',
  };

  const mockMeta = {
    title: 'Blog - ConvertOKit',
    description: 'Digital marketing insights and strategies',
    canonical: 'https://convertokit.com/blog',
    og_title: 'Blog - ConvertOKit',
    og_description: 'Digital marketing insights and strategies',
    twitter_card: 'summary_large_image',
  };

  const defaultProps = {
    blogPosts: mockBlogPosts,
    categories: mockCategories,
    filters: mockFilters,
    meta: mockMeta,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.location.search
    Object.defineProperty(window, 'location', {
      value: {
        search: '',
      },
      writable: true,
    });
  });

  it('renders all blog posts correctly', () => {
    render(<Blog {...defaultProps} />);

    expect(screen.getByText('Meta Ads Best Practices')).toBeInTheDocument();
    expect(screen.getByText('Facebook Pixel Implementation Guide')).toBeInTheDocument();
  });

  it('renders search input with placeholder', () => {
    render(<Blog {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search articles...');
    expect(searchInput).toBeInTheDocument();
  });

  it('renders category filter dropdown', () => {
    render(<Blog {...defaultProps} />);

    expect(screen.getByText('All Categories')).toBeInTheDocument();
  });

  it('renders sort options dropdown', () => {
    render(<Blog {...defaultProps} />);

    expect(screen.getByText('Latest First')).toBeInTheDocument();
  });

  it('handles search input changes with debounce', async () => {
    const user = userEvent.setup();
    render(<Blog {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search articles...');
    
    await user.type(searchInput, 'Meta');

    // Should update the input value immediately
    expect(searchInput).toHaveValue('Meta');

    // Should call router.get after debounce delay
    await waitFor(() => {
      expect(router.get).toHaveBeenCalledWith(
        '/blog',
        expect.objectContaining({ search: 'Meta' }),
        expect.objectContaining({
          preserveState: true,
          preserveScroll: true,
        })
      );
    }, { timeout: 1000 });
  });

  it('handles category filter changes', async () => {
    const user = userEvent.setup();
    render(<Blog {...defaultProps} />);

    // Find and click the category dropdown
    const categoryTrigger = screen.getByText('All Categories');
    await user.click(categoryTrigger);

    // Select a category
    const metaCategory = screen.getByText('Meta Advertising');
    await user.click(metaCategory);

    await waitFor(() => {
      expect(router.get).toHaveBeenCalledWith(
        '/blog',
        expect.objectContaining({ category_id: '1' }),
        expect.objectContaining({
          preserveState: true,
          preserveScroll: true,
        })
      );
    });
  });

  it('handles sort option changes', async () => {
    const user = userEvent.setup();
    render(<Blog {...defaultProps} />);

    // Find and click the sort dropdown
    const sortTrigger = screen.getByText('Latest First');
    await user.click(sortTrigger);

    // Select a sort option
    const titleSort = screen.getByText('Title (A-Z)');
    await user.click(titleSort);

    await waitFor(() => {
      expect(router.get).toHaveBeenCalledWith(
        '/blog',
        expect.objectContaining({ 
          sort_by: 'title',
          sort_direction: 'asc'
        }),
        expect.objectContaining({
          preserveState: true,
          preserveScroll: true,
        })
      );
    });
  });

  it('displays active filters when filters are applied', () => {
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category_id: '1',
        sort_by: 'title',
        sort_direction: 'asc',
      },
    };

    render(<Blog {...propsWithFilters} />);

    expect(screen.getByText('Search: "Meta"')).toBeInTheDocument();
    expect(screen.getByText('Category: Meta Advertising')).toBeInTheDocument();
  });

  it('shows clear filters button when filters are active', () => {
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category_id: '1',
        sort_by: 'title',
        sort_direction: 'asc',
      },
    };

    render(<Blog {...propsWithFilters} />);

    const clearButton = screen.getByText('Clear');
    expect(clearButton).toBeInTheDocument();
  });

  it('handles clear filters button click', async () => {
    const user = userEvent.setup();
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category_id: '1',
        sort_by: 'title',
        sort_direction: 'asc',
      },
    };

    render(<Blog {...propsWithFilters} />);

    const clearButton = screen.getByText('Clear');
    await user.click(clearButton);

    expect(router.get).toHaveBeenCalledWith(
      '/blog',
      {},
      expect.objectContaining({
        preserveState: true,
        preserveScroll: true,
      })
    );
  });

  it('renders pagination controls when multiple pages exist', () => {
    render(<Blog {...defaultProps} />);

    expect(screen.getByText('Showing page 1 of 3')).toBeInTheDocument();
    expect(screen.getByText('Previous')).toBeInTheDocument();
    expect(screen.getByText('Next')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('handles pagination navigation', async () => {
    const user = userEvent.setup();
    render(<Blog {...defaultProps} />);

    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    expect(router.get).toHaveBeenCalledWith(
      '/blog',
      expect.objectContaining({ page: '2' }),
      expect.objectContaining({
        preserveState: true,
        preserveScroll: false,
      })
    );
  });

  it('handles page number clicks', async () => {
    const user = userEvent.setup();
    render(<Blog {...defaultProps} />);

    const pageButton = screen.getByText('3');
    await user.click(pageButton);

    expect(router.get).toHaveBeenCalledWith(
      '/blog',
      expect.objectContaining({ page: '3' }),
      expect.objectContaining({
        preserveState: true,
        preserveScroll: false,
      })
    );
  });

  it('disables previous button on first page', () => {
    render(<Blog {...defaultProps} />);

    const previousButton = screen.getByText('Previous');
    expect(previousButton).toBeDisabled();
  });

  it('disables next button on last page', () => {
    const lastPageProps = {
      ...defaultProps,
      blogPosts: {
        ...mockBlogPosts,
        current_page: 3,
        next_page_url: null,
      },
    };

    render(<Blog {...lastPageProps} />);

    const nextButton = screen.getByText('Next');
    expect(nextButton).toBeDisabled();
  });

  it('displays coming soon message when no posts exist', () => {
    const propsWithNoPosts = {
      ...defaultProps,
      blogPosts: { data: [] },
    };

    render(<Blog {...propsWithNoPosts} />);

    expect(screen.getByText('Coming Soon')).toBeInTheDocument();
    expect(screen.getByText("We're working on creating valuable content for you.")).toBeInTheDocument();
  });

  it('renders blog post cards with correct information', () => {
    render(<Blog {...defaultProps} />);

    // Check for post titles
    expect(screen.getByText('Meta Ads Best Practices')).toBeInTheDocument();
    expect(screen.getByText('Facebook Pixel Implementation Guide')).toBeInTheDocument();

    // Check for post excerpts
    expect(screen.getByText('Learn the best practices for Meta advertising campaigns')).toBeInTheDocument();
    expect(screen.getByText('Complete guide to implementing Facebook Pixel')).toBeInTheDocument();

    // Check for author names
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();

    // Check for categories
    expect(screen.getByText('Meta Advertising')).toBeInTheDocument();
    expect(screen.getByText('Tracking & Analytics')).toBeInTheDocument();
  });

  it('preserves filters in pagination URLs', async () => {
    const user = userEvent.setup();
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category_id: '1',
        sort_by: 'title',
        sort_direction: 'asc',
      },
    };

    // Mock window.location.search to include existing filters
    Object.defineProperty(window, 'location', {
      value: {
        search: '?search=Meta&category_id=1&sort_by=title&sort_direction=asc',
      },
      writable: true,
    });

    render(<Blog {...propsWithFilters} />);

    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    expect(router.get).toHaveBeenCalledWith(
      '/blog',
      expect.objectContaining({
        page: '2',
        search: 'Meta',
        category_id: '1',
        sort_by: 'title',
        sort_direction: 'asc',
      }),
      expect.objectContaining({
        preserveState: true,
        preserveScroll: false,
      })
    );
  });
});
